# 第四问拐点切换未执行问题调试分析

## 问题现象

用户添加了调试输出`my_printf(&huart1, "a\r\n");`但没有看到输出，说明拐点切换条件`last_in_dead_zone && !in_dead_zone`从未被满足。

## 根本原因分析

### 1. 逻辑流程分析

```c
void imu_four_corner_control(float current_yaw, float last_yaw, bool in_dead_zone)
{
    static bool last_in_dead_zone = true;  // ⚠️ 初始化为true
    static bool first_run = true;
    
    if(first_run) {
        first_run = false;  // 首次运行，跳过切换逻辑
    }
    else if(last_in_dead_zone && !in_dead_zone) {  // ⚠️ 这里是问题所在
        // 切换拐点的代码
    }
    
    // ...
    
    last_in_dead_zone = in_dead_zone;  // ⚠️ 最后更新状态
}
```

### 2. 调用逻辑分析

```c
case task4_state3:  
    bool in_dead_zone = is_in_dead_zone(hwt_yaw);
    
    if(in_dead_zone) {
        task4_track_control();  // 死区内：不调用imu_four_corner_control
    } else {
        imu_four_corner_control(hwt_yaw, last_yaw, in_dead_zone);  // 非死区：调用
    }
break;
```

## 🚨 **发现的关键问题**

### 问题1：函数只在非死区被调用
- **现状**: `imu_four_corner_control()`只在`!in_dead_zone`时被调用
- **结果**: 函数内的`in_dead_zone`参数永远是`false`
- **影响**: `last_in_dead_zone && !in_dead_zone`中的`!in_dead_zone`永远是`true`，但`last_in_dead_zone`可能不对

### 问题2：last_in_dead_zone状态更新不完整
- **现状**: 只有在非死区时才更新`last_in_dead_zone = in_dead_zone`
- **结果**: 当在死区时，`last_in_dead_zone`状态不会被更新
- **影响**: 状态跟踪不准确

### 问题3：初始状态设置问题
- **现状**: `static bool last_in_dead_zone = true;`
- **问题**: 如果系统启动时就在非死区，那么第一次调用时条件就满足了
- **但是**: 由于`first_run`标志，第一次会跳过切换逻辑

## 修正方案

### 方案1：调整调用逻辑（推荐）

修改`task4_proc2()`中的调用逻辑：

```c
case task4_state3:  
    Relay(1);
    bool in_dead_zone = is_in_dead_zone(hwt_yaw);
    
    // 始终调用IMU控制函数来更新状态
    imu_four_corner_control(hwt_yaw, last_yaw, in_dead_zone);
    
    if(in_dead_zone) {
        // 死区内使用视觉控制
        task4_track_control();
    }
    // 注意：非死区时IMU控制已在imu_four_corner_control内部处理
    
    last_yaw = hwt_yaw;
break;
```

### 方案2：修改IMU控制函数逻辑

```c
void imu_four_corner_control(float current_yaw, float last_yaw, bool in_dead_zone)
{
    static bool last_in_dead_zone = true;
    static bool first_run = true;
    
    // 添加调试输出
    my_printf(&huart1, "IMU: dead=%d, last_dead=%d, first=%d\r\n", 
              in_dead_zone, last_in_dead_zone, first_run);
    
    if(first_run) {
        first_run = false;
        my_printf(&huart1, "First run, corner:%d\r\n", current_corner + 1);
    }
    else if(last_in_dead_zone && !in_dead_zone) {
        current_corner = (current_corner + 1) % 4;
        my_printf(&huart1, "Corner switched to:%d\r\n", current_corner + 1);
    }
    
    // 只在非死区时执行IMU控制
    if(!in_dead_zone) {
        // IMU控制逻辑...
    }
    
    // 始终更新状态
    last_in_dead_zone = in_dead_zone;
}
```

## 调试建议

### 立即调试步骤

1. **添加状态调试输出**：
```c
// 在imu_four_corner_control函数开头添加
my_printf(&huart1, "DEBUG: dead=%d, last_dead=%d, yaw=%.1f\r\n", 
          in_dead_zone, last_in_dead_zone, current_yaw);
```

2. **添加死区判断调试**：
```c
// 在task4_proc2中添加
bool in_dead_zone = is_in_dead_zone(hwt_yaw);
my_printf(&huart1, "Dead zone check: yaw=%.1f, dead=%d\r\n", hwt_yaw, in_dead_zone);
```

3. **验证函数调用**：
```c
// 在imu_four_corner_control函数开头添加
my_printf(&huart1, "IMU function called\r\n");
```

### 预期的调试输出序列

正常情况下应该看到：
```
Dead zone check: yaw=XXX, dead=1    // 在死区
Dead zone check: yaw=XXX, dead=0    // 离开死区
IMU function called                  // 函数被调用
DEBUG: dead=0, last_dead=1, yaw=XXX  // 状态检查
Corner switched to:X                 // 拐点切换成功
```

## 总结

问题的根本原因是**函数调用逻辑设计不当**，导致状态跟踪不完整。建议采用方案1，让IMU控制函数始终被调用以正确跟踪死区状态变化。
