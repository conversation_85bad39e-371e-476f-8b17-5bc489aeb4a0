#include "interrupt.h"

#define STATE_DELAY_MS 200  // 状态延迟时间200ms
#define WAIT_DELAY_MS 200  // 等待到位延迟时间200ms

void task4_proc2(void);

// 初始化四拐点补偿数据
void init_four_corner_compensation(void)
{
    // 设置您的四个拐点数据
    set_corner_compensation(0, 114.1f, 90.0f);  // 第1个拐点
    set_corner_compensation(1, 90.0f, 90.0f);    // 第2个拐点 - 请填入您的数据
    set_corner_compensation(2, 98.9f, 90.0f);    // 第3个拐点 - 请填入您的数据  
    set_corner_compensation(3, 86.5f, 90.0f);    // 第4个拐点 - 请填入您的数据
    
   // my_printf(&huart1, "四拐点补偿系统初始化完成\r\n");
}

struct state_machine State_Machine;
uint16_t task2_count;
uint16_t task3_count;
uint32_t state_start_time = 0;  // 状态开始时间戳
uint16_t arrive_flag;
void task4_proc(void);
void task3_proc(void);
int16_t count1;
int16_t count2;
void task4_hybrid_control(void);

void test_imu_simple(void);
void State_Machine_init()
{
    State_Machine.MAIN_STATE = STATE_IDLE;
    State_Machine.STATE_TASK2 = task2_state0;
    State_Machine.STATE_TASK3 = task3_state0;
    State_Machine.STATE_TASK4 = STATE_IDLE;//没改
}

void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
	if(htim -> Instance == TIM6)//20ms进入一次中断
	{
		switch(State_Machine.MAIN_STATE)
		{
			case STATE_IDLE://IDLE
				
			break;
			case TASK_3: //第三问逻辑
			{
				task3_proc();
			}
			break;
			case TASK_4: //第四问逻辑
			{
				task4_proc2();
			}
			break;
			default:
				
			break;
		}
		
	}
	
}

void task3_proc()
{
	
	switch(State_Machine.STATE_TASK3)
	{
		case task3_state0:
			Relay(0);
			if(camera_x_error == 0 &&  camera_y_error == 0)
				State_Machine.STATE_TASK3 = task3_state1;
			else 
				State_Machine.STATE_TASK3 = task3_state2;
		break;
		case task3_state1:
			Auto_find();
		break;
		case task3_state2:
			track_control();
			if(arrive_flag) 
			{
				if(++task3_count == 10)
				{
					task3_count = 0;
					arrive_flag = 0;
					State_Machine.STATE_TASK3 = task3_state3;
				}
			}
		break;
		case task3_state3:
			Relay(1);
			track_control();
			//State_Machine.STATE_TASK3 = task3_state0;
			//State_Machine.MAIN_STATE = STATE_IDLE;
		break;
	}
}

void task4_proc()
{
	switch(State_Machine.STATE_TASK4)
	{
		case task4_state0:  // 复用task3的状态定义
			Relay(0);
			if(camera_x_error == 0 &&  camera_y_error == 0)
				State_Machine.STATE_TASK4 = task4_state1;
			else
				State_Machine.STATE_TASK4 = task4_state2;
		break;
		case task4_state1:
			Auto_find_task4();
		break;
		case task4_state2:
			task4_track_control();  // 使用TASK4专用自适应PID
			if(arrive_flag)
			{
				if(++task3_count == 10)
				{
					task3_count = 0;
					arrive_flag = 0;
					State_Machine.STATE_TASK4 = task4_state3;
				}
			}
		break;
		case task4_state3:
			Relay(1);
			task4_track_control();  // 使用TASK4专用自适应PID
		break;
	}
}
// 死区判断函数
bool is_in_dead_zone(float yaw_360) {
    return (yaw_360 >= 355.0f || yaw_360 <= 5.0f) ||      // AB段 (0°±5°)
           (yaw_360 >= 85.0f && yaw_360 <= 95.0f) ||      // BC段 (90°±5°)
           (yaw_360 >=175.0f && yaw_360 <= 195.0f) ||    // CD段 (180°±5°)
           (yaw_360 >= 265.0f && yaw_360 <= 285.0f);      // DA段 (270°±5°)
}

// 在task4_state3中使用四拐点控制
void task4_proc2()
{
	
    static float last_yaw = 0.0f;
    
    switch(State_Machine.STATE_TASK4)
    {
        case task4_state0:  
            Relay(0);
            if(camera_x_error == 0 &&  camera_y_error == 0)
                State_Machine.STATE_TASK4 = task4_state1;
            else
                State_Machine.STATE_TASK4 = task4_state2;
        break;
        case task4_state1:  
            Auto_find_task4();
        break;
        case task4_state2:  
            task4_track_control();
            if(arrive_flag)
            {
                if(++task3_count == 10)
                {
                    task3_count = 0;
                    arrive_flag = 0;
                    State_Machine.STATE_TASK4 = task4_state3;
                    last_yaw = hwt_yaw;  // 初始化上次角度
                }
            }
        break;  
        case task4_state3:
            Relay(1);
            // 使用四拐点IMU控制
            bool in_dead_zone = is_in_dead_zone(hwt_yaw);

            // ✅ 始终调用IMU控制函数来正确跟踪死区状态变化
            imu_four_corner_control(hwt_yaw, last_yaw, in_dead_zone);

            if(in_dead_zone)
            {
                // 死区内额外使用视觉控制
                task4_track_control();
            }

            last_yaw = hwt_yaw;  // 更新上次角度
        break;
    }
}
