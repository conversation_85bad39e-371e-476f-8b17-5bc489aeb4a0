# 第四问状态机代码分析文档

## 文档信息
- **版本**: v1.0
- **创建时间**: 2025-01-02
- **负责人**: <PERSON> (Engineer)
- **项目**: 2025laserV4.5 激光打靶系统

## 概述

第四问状态机实现了基于视觉和IMU混合控制的激光打靶系统，具备四拐点补偿功能，能够在不同区域自动切换控制策略。

## 核心架构

### 1. 状态机结构

```c
struct state_machine {
    int MAIN_STATE;      // 主状态：STATE_IDLE, TASK_2, TASK_3, TASK_4
    int STATE_TASK2;     // 第二问状态
    int STATE_TASK3;     // 第三问状态  
    int STATE_TASK4;     // 第四问状态
};
```

### 2. 第四问子状态定义

```c
#define task4_state0 0   // 初始状态：检测目标
#define task4_state1 1   // 自动寻找状态
#define task4_state2 2   // 视觉追踪状态
#define task4_state3 3   // 混合控制状态（视觉+IMU四拐点）
```

## 状态机执行流程

### 主状态机入口
- **触发**: 20ms定时器中断 (TIM6)
- **入口函数**: `HAL_TIM_PeriodElapsedCallback()`
- **执行函数**: `task4_proc2()`

### 状态转换逻辑

#### task4_state0 (初始检测状态)
```c
case task4_state0:  
    Relay(0);  // 关闭激光
    if(camera_x_error == 0 && camera_y_error == 0)
        State_Machine.STATE_TASK4 = task4_state1;  // 无目标→自动寻找
    else
        State_Machine.STATE_TASK4 = task4_state2;  // 有目标→视觉追踪
break;
```

#### task4_state1 (自动寻找状态)
```c
case task4_state1:  
    Auto_find_task4();  // 执行自动寻找算法
break;
```

#### task4_state2 (视觉追踪状态)
```c
case task4_state2:  
    task4_track_control();  // TASK4专用自适应PID控制
    if(arrive_flag) {
        if(++task3_count == 10) {  // 到位确认计数
            task3_count = 0;
            arrive_flag = 0;
            State_Machine.STATE_TASK4 = task4_state3;  // 进入混合控制
            last_yaw = hwt_yaw;  // 初始化IMU角度
        }
    }
break;
```

#### task4_state3 (混合控制状态)
```c
case task4_state3:  
    Relay(1);  // 开启激光
    bool in_dead_zone = is_in_dead_zone(hwt_yaw);  // 死区判断
    
    if(in_dead_zone) {
        task4_track_control();  // 死区内使用视觉控制
    } else {
        imu_four_corner_control(hwt_yaw, last_yaw, in_dead_zone);  // 非死区使用IMU四拐点控制
    }
    
    last_yaw = hwt_yaw;  // 更新角度记录
break;
```

## 核心控制算法

### 1. 死区判断算法

```c
bool is_in_dead_zone(float yaw_360) {
    return (yaw_360 >= 355.0f || yaw_360 <= 5.0f) ||      // AB段 (0°±5°)
           (yaw_360 >= 85.0f && yaw_360 <= 95.0f) ||      // BC段 (90°±5°)
           (yaw_360 >=175.0f && yaw_360 <= 195.0f) ||     // CD段 (180°±5°)
           (yaw_360 >= 265.0f && yaw_360 <= 285.0f);      // DA段 (270°±5°)
}
```

**功能**: 判断当前IMU角度是否处于四个拐点的死区范围内（±5°）

### 2. 四拐点IMU控制算法

#### 核心参数
```c
// 四拐点云台补偿角度数组
float gimbal_compensation_angles[4] = {
    114.1f,  // 第1个拐点：车转90°，云台转114.1°
    70.0f,   // 第2个拐点
    78.9f,   // 第3个拐点  
    86.5f    // 第4个拐点
};

// 四拐点对应的车辆转动角度
float car_rotation_angles[4] = {90.0f, 90.0f, 90.0f, 90.0f};

// 当前拐点索引
static uint8_t current_corner = 0;
```

#### 控制逻辑
```c
void imu_four_corner_control(float current_yaw, float last_yaw, bool in_dead_zone) {
    // 1. 拐点切换逻辑
    if(last_in_dead_zone && !in_dead_zone) {
        current_corner = (current_corner + 1) % 4;  // 离开死区时切换拐点
    }
    
    // 2. IMU角度控制
    if(!in_dead_zone) {
        float delta_yaw = current_yaw - last_yaw;
        
        // 处理角度跳变
        if (delta_yaw > 180.0f) delta_yaw -= 360.0f;
        if (delta_yaw < -180.0f) delta_yaw += 360.0f;
        
        // 计算补偿比例
        float compensation_ratio = gimbal_compensation_angles[current_corner] / 
                                 car_rotation_angles[current_corner];
        
        // 计算云台转动角度
        float gimbal_delta = delta_yaw * compensation_ratio;
        
        // 转换为脉冲数并执行
        int32_t x_pulse = (int32_t)(gimbal_delta / 0.007f + 0.5f);
        if (abs(x_pulse) > 0) {
            motorA_xiangdui(-x_pulse);
        }
    }
}
```

### 3. TASK4专用自适应PID控制

#### PID参数自适应策略
```c
float TASK4_Adaptive_PID(tPid * pid, float err) {
    float abs_err = fabs(err);
    
    if(pid == &motorA_pid) {  // X轴自适应控制
        if(abs_err > 50.0f) {
            pid->Kp = 0.85;  // 大误差：提高响应速度
            pid->Kd = 1.8;   // 增强阻尼
        } else if(abs_err > 20.0f) {
            // 中等误差：渐变参数
            float ratio = (abs_err - 20.0f) / 30.0f;
            pid->Kp = 0.65 + ratio * (0.85 - 0.65);
            pid->Kd = 1.6 + ratio * (1.8 - 1.6);
        } else {
            pid->Kp = 0.65;  // 小误差：原始参数
            pid->Kd = 1.6;
        }
    } else {  // Y轴保持固定参数
        pid->Kp = 0.5;
        pid->Kd = 1.5;
    }
    
    // PID计算和限幅...
}
```

## 关键数据流

### 输入数据
- **hwt_yaw**: IMU角度数据 (0-360°)
- **camera_x_error**: 摄像头X轴误差 (-255~255)
- **camera_y_error**: 摄像头Y轴误差 (-255~255)
- **arrive_flag**: 到位标志

### 输出控制
- **motorA_xiangdui()**: X轴云台相对位置控制
- **Motor_Set_Speed()**: XY轴速度控制
- **Relay()**: 激光开关控制

## 系统初始化

### 四拐点补偿数据初始化
```c
void init_four_corner_compensation(void) {
    set_corner_compensation(0, 114.1f, 90.0f);  // 第1个拐点
    set_corner_compensation(1, 70.0f, 90.0f);   // 第2个拐点
    set_corner_compensation(2, 78.9f, 90.0f);   // 第3个拐点  
    set_corner_compensation(3, 86.5f, 90.0f);   // 第4个拐点
}
```

### 状态机初始化
```c
void State_Machine_init() {
    State_Machine.MAIN_STATE = STATE_IDLE;
    State_Machine.STATE_TASK4 = STATE_IDLE;
}
```

## 性能特点

### 优势
1. **混合控制策略**: 视觉+IMU双重保障，提高精度
2. **自适应PID**: 根据误差大小动态调整参数
3. **死区处理**: 在IMU不可靠区域自动切换到视觉控制
4. **四拐点补偿**: 针对不同角度区域的个性化补偿

### 技术指标
- **控制周期**: 20ms (50Hz)
- **角度精度**: 0.007°/脉冲
- **死区范围**: ±5°
- **到位判断**: X/Y误差均<5时确认到位

## 调试接口

### 串口输出信息
- 拐点切换信息: `"conner:%d，angle:%.1f°"`
- IMU控制信息: `"A%d: B%.2f° C%.2f° D%ld"`
- 系统初始化: `"四拐点补偿系统初始化完成"`

### 调试函数
- `get_current_corner_info()`: 获取当前拐点信息
- 各种my_printf调试输出

## 文件结构

### 主要文件
- **APP/interrupt.c**: 状态机主逻辑
- **APP/app_motor.c**: 电机控制和四拐点算法
- **APP/pid.c**: PID控制算法
- **APP/usart_app.c**: 数据通信和处理

### 头文件
- **APP/interrupt.h**: 状态定义和结构体
- **APP/app_motor.h**: 电机控制函数声明
- **APP/pid.h**: PID结构体和函数声明

## 详细代码分析

### 1. 状态机核心函数 `task4_proc2()`

<augment_code_snippet path="APP/interrupt.c" mode="EXCERPT">
````c
void task4_proc2()
{
    static float last_yaw = 0.0f;

    switch(State_Machine.STATE_TASK4)
    {
        case task4_state0:
            Relay(0);
            if(camera_x_error == 0 &&  camera_y_error == 0)
                State_Machine.STATE_TASK4 = task4_state1;
            else
                State_Machine.STATE_TASK4 = task4_state2;
        break;
        case task4_state1:
            Auto_find_task4();
        break;
        case task4_state2:
            task4_track_control();
            if(arrive_flag)
            {
                if(++task3_count == 10)
                {
                    task3_count = 0;
                    arrive_flag = 0;
                    State_Machine.STATE_TASK4 = task4_state3;
                    last_yaw = hwt_yaw;  // 初始化上次角度
                }
            }
        break;
        case task4_state3:
            Relay(1);
            // 使用四拐点IMU控制
            bool in_dead_zone = is_in_dead_zone(hwt_yaw);

            if(in_dead_zone)
            {
                // 在死区使用视觉控制
                task4_track_control();
            }
            else
            {
                // 在非死区使用四拐点IMU控制
                imu_four_corner_control(hwt_yaw, last_yaw, in_dead_zone);
            }

            last_yaw = hwt_yaw;  // 更新上次角度
        break;
    }
}
````
</augment_code_snippet>

### 2. 四拐点IMU控制核心算法

<augment_code_snippet path="APP/app_motor.c" mode="EXCERPT">
````c
void imu_four_corner_control(float current_yaw, float last_yaw, bool in_dead_zone)
{
    static bool last_in_dead_zone = true;
    static bool first_run = true;  // 添加首次运行标志

    // 首次运行时不切换拐点
    if(first_run)
    {
        first_run = false;
        my_printf(&huart1, "conner:%d，angle:%.1f°\r\n",
                 current_corner + 1, gimbal_compensation_angles[current_corner]);
    }
    // 检测是否刚离开死区（非首次运行）
    else if(last_in_dead_zone && !in_dead_zone)
    {
        // 刚离开死区，切换到下一个拐点
        current_corner = (current_corner + 1) % 4;
        my_printf(&huart1, "conner:%d，angle:%.1f°\r\n",
                 current_corner + 1, gimbal_compensation_angles[current_corner]);
    }

    if(!in_dead_zone)  // 在非死区时使用IMU控制
    {
        float delta_yaw = current_yaw - last_yaw;

        // 处理角度跳变
        if (delta_yaw > 180.0f) delta_yaw -= 360.0f;
        if (delta_yaw < -180.0f) delta_yaw += 360.0f;

        // 小于阈值不控制
        if (fabs(delta_yaw) < 0.1f) return;

        // 计算当前拐点的补偿比例
        float compensation_ratio = gimbal_compensation_angles[current_corner] / car_rotation_angles[current_corner];

        // 计算云台需要转动的角度
        float gimbal_delta = delta_yaw * compensation_ratio;

        // 转换为脉冲数 (1脉冲 = 0.007度)
        int32_t x_pulse = (int32_t)(gimbal_delta / 0.007f + 0.5f);

        if (abs(x_pulse) > 0) {
            motorA_xiangdui(-x_pulse);
            my_printf(&huart1, "A%d: B%.2f° C%.2f° D%ld\r\n",
                     current_corner + 1, delta_yaw, gimbal_delta, x_pulse);
        }
    }

    // 更新死区状态
    last_in_dead_zone = in_dead_zone;
}
````
</augment_code_snippet>

### 3. 自适应PID控制算法

<augment_code_snippet path="APP/pid.c" mode="EXCERPT">
````c
float TASK4_Adaptive_PID(tPid * pid, float err)
{
    pid->err = err;
    float abs_err = fabs(err);

    // TASK4自适应参数调节 - 只优化X轴
    if(pid == &motorA_pid) {
        // X轴自适应控制
        if(abs_err > 50.0f) {
            // 大误差：轻微提高响应速度
            pid->Kp = 0.85;  // 轻微提高（约1.3倍）
            pid->Kd = 1.8;   // 轻微增强阻尼
        } else if(abs_err > 20.0f) {
            // 中等误差：渐变参数
            float ratio = (abs_err - 20.0f) / 30.0f;
            pid->Kp = 0.65 + ratio * (0.85 - 0.65);
            pid->Kd = 1.6 + ratio * (1.8 -   1.6);
        } else {
            // 小误差：使用原始参数
            pid->Kp = 0.65;
            pid->Kd = 1.6;
        }
    } else {
        // Y轴保持原始TASK4参数不变
        pid->Kp = 0.5;
        pid->Kd = 1.5;
    }

    // 计算PID输出
    pid->err_sum += pid->err;
    pid->output = pid->Kp * pid->err +
                  pid->Ki * pid->err_sum +
                  pid->Kd * (pid->err - pid->err_last);

    // 到位判断
    if(fabs(motorA_pid.err) < 5 && fabs(motorB_pid.err) < 5)
        arrive_flag = 1;

    // 积分限幅
    if(pid->err_sum > 400.0f) pid->err_sum = 400.0f;
    else if(pid->err_sum < -400.0f) pid->err_sum = -400.0f;

    // 输出限幅
    if(pid->output > 80.0f) pid->output = 80.0f;
    else if(pid->output < -80.0f) pid->output = -80.0f;

    pid->err_last = pid->err;
    return pid->output;
}
````
</augment_code_snippet>

## 系统工作原理深度解析

### 1. 混合控制策略原理

第四问采用了创新的混合控制策略，根据IMU角度所处的区域动态选择控制方式：

- **死区内（±5°范围）**: 使用视觉PID控制，因为在拐点附近IMU数据可能不稳定
- **非死区**: 使用IMU四拐点补偿控制，利用预设的补偿角度实现精确控制

### 2. 四拐点补偿机制

系统预设了四个拐点的补偿数据：
- 拐点1: 车转90°，云台需转114.1°（补偿比例1.268）
- 拐点2: 车转90°，云台需转70.0°（补偿比例0.778）
- 拐点3: 车转90°，云台需转78.9°（补偿比例0.877）
- 拐点4: 车转90°，云台需转86.5°（补偿比例0.961）

这种设计考虑了机械结构的非线性特性和环境因素的影响。

### 3. 状态转换时序

```
STATE_IDLE → task4_state0 → task4_state1/task4_state2 → task4_state3
     ↑            ↓              ↓                        ↓
   初始化      目标检测      自动寻找/视觉追踪          混合控制
```

### 4. 数据处理流程

1. **IMU数据处理**: 将-180°~+180°映射到0°~360°
2. **视觉数据处理**: 摄像头误差值范围-255~255
3. **角度跳变处理**: 处理359°→1°的跳变情况
4. **脉冲转换**: 角度转换为电机脉冲（1脉冲=0.007°）

## 关键技术特点

### 1. 实时性保障
- 20ms定时器中断确保控制周期稳定
- 状态机设计避免阻塞操作
- 快速的数据处理和决策逻辑

### 2. 鲁棒性设计
- 多重限幅保护（积分限幅、输出限幅）
- 死区检测避免不稳定控制
- 角度跳变处理确保连续性

### 3. 精度优化
- 自适应PID根据误差大小调整参数
- 四拐点个性化补偿提高精度
- 0.1°的控制阈值避免微小抖动

## 潜在优化方向

### 1. 参数自适应
- 可考虑根据环境条件动态调整死区范围
- 补偿角度可通过学习算法自动优化

### 2. 控制策略
- 可增加预测控制算法提高响应速度
- 考虑加入卡尔曼滤波提高数据质量

### 3. 系统集成
- 可增加故障检测和恢复机制
- 考虑多传感器融合提高可靠性

## 总结

第四问状态机实现了一个完整的混合控制系统，通过状态机管理不同的控制阶段，结合视觉追踪和IMU四拐点补偿，实现了高精度的激光打靶控制。系统具备良好的鲁棒性和适应性，能够在复杂环境下稳定工作。

关键创新点：
1. **混合控制策略**: 视觉+IMU智能切换
2. **四拐点补偿**: 个性化角度补偿机制
3. **自适应PID**: 动态参数调整
4. **实时状态机**: 高效的控制流程管理

该系统为激光打靶应用提供了一个高性能、高可靠性的控制解决方案。
