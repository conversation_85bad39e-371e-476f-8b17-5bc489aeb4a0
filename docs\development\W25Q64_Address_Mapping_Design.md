# W25Q64地址映射统一设计方案

**设计目标**: 解决底层驱动地址计算不一致问题，确保擦除、写入、读取操作的地址完全匹配  
**设计时间**: 2025-01-30  
**设计人员**: <PERSON> (米醋电子工作室)  
**项目**: 2025laserV2.5

## 1. 当前地址计算问题总结

### 1.1 三个函数的地址处理方式

| 函数 | 地址处理 | 示例输入 | 实际地址 | 问题 |
|------|----------|----------|----------|------|
| W25QXX_Erase_Sector | `addr *= 4096; addr <<= 8` | 100 | 104,857,600 | 超出容量 |
| W25QXX_Page_Program | `addr <<= 8` | 0x0064 | 0x6400 | 正常 |
| W25QXX_Read | `addr <<= 8` | 0x0064 | 0x6400 | 正常 |

### 1.2 核心问题
- **擦除函数**：地址计算错误，导致擦除无效或错误区域
- **写入读取函数**：地址计算一致，但与擦除地址不匹配
- **结果**：第二次写入时，旧数据未被擦除，新数据写入失败

## 2. 统一地址映射策略设计

### 2.1 设计原则
1. **地址一致性**：确保擦除、写入、读取操作指向相同的物理地址
2. **简化计算**：避免复杂的地址转换，减少出错可能
3. **容量安全**：确保所有地址都在W25Q64的8MB范围内
4. **性能优化**：减少擦除次数，提高Flash寿命

### 2.2 推荐方案：同扇区不同页地址

**核心思路**：使用扇区0内的不同页地址存储X和Y轴数据

```c
// 统一地址映射定义
#define FLASH_BASE_SECTOR       0           // 使用扇区0
#define PULSE_X_PAGE_ADDR       0x0000      // X轴数据页地址
#define PULSE_Y_PAGE_ADDR       0x0001      // Y轴数据页地址

// 地址计算验证
// 扇区0擦除：0 * 4096 * 256 = 0x000000 ✓
// X轴写入：0x0000 << 8 = 0x000000 ✓  
// Y轴写入：0x0001 << 8 = 0x000100 ✓
// X轴读取：0x0000 << 8 = 0x000000 ✓
// Y轴读取：0x0001 << 8 = 0x000100 ✓
```

### 2.3 地址映射表

| 数据类型 | 传入参数 | 实际物理地址 | 地址范围 | 说明 |
|----------|----------|--------------|----------|------|
| 扇区擦除 | 0 | 0x000000 | 0x000000-0x000FFF | 扇区0 (4KB) |
| X轴数据 | 0x0000 | 0x000000 | 0x000000-0x000003 | 4字节 |
| Y轴数据 | 0x0001 | 0x000100 | 0x000100-0x000103 | 4字节 |

### 2.4 优势分析

**可靠性**：
- 所有操作都在同一扇区内，地址匹配100%
- 避免了地址溢出和计算错误
- 一次擦除支持两个数据的写入

**性能**：
- 减少擦除次数（一次擦除vs两次擦除）
- 地址连续，提高SPI通信效率
- 简化地址计算，减少CPU开销

**维护性**：
- 地址映射清晰明确
- 易于理解和调试
- 符合Flash存储的最佳实践

## 3. 实现方案

### 3.1 修改后的存储函数

```c
bool Pulse_Save_Values(int32_t pos_x, int32_t pos_y)
{
    // 1. 擦除扇区0 (一次擦除，支持两个数据)
    W25QXX_Erase_Sector(FLASH_BASE_SECTOR);
    HAL_Delay(300);  // 确保擦除完成
    
    // 2. 手动转换X轴数据为字节数组
    uint8_t x_data[4] = {
        (pos_x >> 0) & 0xFF,   // LSB
        (pos_x >> 8) & 0xFF,
        (pos_x >> 16) & 0xFF,
        (pos_x >> 24) & 0xFF   // MSB
    };
    
    // 3. 写入X轴数据到地址0x000000
    W25QXX_Page_Program(x_data, PULSE_X_PAGE_ADDR, 4);
    HAL_Delay(50);
    
    // 4. 手动转换Y轴数据为字节数组
    uint8_t y_data[4] = {
        (pos_y >> 0) & 0xFF,   // LSB
        (pos_y >> 8) & 0xFF,
        (pos_y >> 16) & 0xFF,
        (pos_y >> 24) & 0xFF   // MSB
    };
    
    // 5. 写入Y轴数据到地址0x000100
    W25QXX_Page_Program(y_data, PULSE_Y_PAGE_ADDR, 4);
    HAL_Delay(50);
    
    return true;
}

bool Pulse_Read_Values(int32_t *pos_x, int32_t *pos_y)
{
    uint8_t x_data[4], y_data[4];
    
    // 1. 读取X轴数据
    if (W25QXX_Read(x_data, PULSE_X_PAGE_ADDR, 4) != 0) {
        return false;
    }
    
    // 2. 读取Y轴数据
    if (W25QXX_Read(y_data, PULSE_Y_PAGE_ADDR, 4) != 0) {
        return false;
    }
    
    // 3. 手动转换字节数组为int32_t (小端序)
    *pos_x = (int32_t)((x_data[3] << 24) | (x_data[2] << 16) | 
                       (x_data[1] << 8) | x_data[0]);
    *pos_y = (int32_t)((y_data[3] << 24) | (y_data[2] << 16) | 
                       (y_data[1] << 8) | y_data[0]);
    
    return true;
}
```

## 4. 验证测试

### 4.1 地址计算验证

```c
// 验证地址计算的数学正确性
void verify_address_calculation(void)
{
    // 扇区擦除地址计算
    uint32_t erase_addr = FLASH_BASE_SECTOR * 4096 * 256;  // = 0
    printf("擦除地址: 0x%08X\n", erase_addr);
    
    // X轴写入地址计算  
    uint32_t x_write_addr = PULSE_X_PAGE_ADDR << 8;  // = 0x000000
    printf("X轴写入地址: 0x%08X\n", x_write_addr);
    
    // Y轴写入地址计算
    uint32_t y_write_addr = PULSE_Y_PAGE_ADDR << 8;  // = 0x000100
    printf("Y轴写入地址: 0x%08X\n", y_write_addr);
    
    // 验证地址范围
    assert(erase_addr < 8*1024*1024);      // < 8MB
    assert(x_write_addr < 8*1024*1024);    // < 8MB  
    assert(y_write_addr < 8*1024*1024);    // < 8MB
}
```

### 4.2 功能测试用例

```c
void test_address_mapping(void)
{
    int32_t test_x = -400, test_y = -1200;
    int32_t read_x, read_y;
    
    // 第一次写入测试
    assert(Pulse_Save_Values(test_x, test_y) == true);
    assert(Pulse_Read_Values(&read_x, &read_y) == true);
    assert(read_x == test_x && read_y == test_y);
    
    // 第二次写入测试 (关键测试)
    test_x = 3500; test_y = 400;
    assert(Pulse_Save_Values(test_x, test_y) == true);
    assert(Pulse_Read_Values(&read_x, &read_y) == true);
    assert(read_x == test_x && read_y == test_y);
    
    printf("地址映射测试通过！\n");
}
```

## 5. 总结

### 5.1 解决的问题
- ✅ 修复了地址计算不一致的根本问题
- ✅ 确保擦除、写入、读取操作地址完全匹配
- ✅ 避免了地址溢出和无效操作
- ✅ 提高了存储操作的可靠性

### 5.2 技术优势
- **简单可靠**：地址计算逻辑清晰明确
- **性能优化**：减少擦除次数，提高效率
- **易于维护**：代码结构清晰，便于调试
- **向后兼容**：API接口保持不变

### 5.3 下一步行动
1. 更新pulse_storage.h中的地址定义
2. 重构pulse_storage.c中的存储函数
3. 添加地址计算验证测试
4. 进行全面的功能测试验证

---
*地址映射设计完成 - 米醋电子工作室技术团队*
