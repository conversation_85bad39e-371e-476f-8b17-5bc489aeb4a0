#include "W25Q64.h"

extern SPI_HandleTypeDef hspi1;

/**
 * @brief    SPI发送指定长度的数据
 * @param    buf  —— 发送数据缓冲区首地址
 * @param    size —— 要发送数据的字节数
 * @retval   成功返回HAL_OK
 */
static HAL_StatusTypeDef SPI_Transmit(uint8_t* send_buf, uint16_t size)
{
    return HAL_SPI_Transmit(&hspi1, send_buf, size, 100);
}


/**
 * @brief   SPI接收指定长度的数据
 * @param   buf  —— 接收数据缓冲区首地址
 * @param   size —— 要接收数据的字节数
 * @retval  成功返回HAL_OK
 */
static HAL_StatusTypeDef SPI_Receive(uint8_t* recv_buf, uint16_t size)
{
   return HAL_SPI_Receive(&hspi1, recv_buf, size, 100);
}

/**
 * @brief   SPI在发送数据的同时接收指定长度的数据
 * @param   send_buf  —— 接收数据缓冲区首地址
 * @param   recv_buf  —— 接收数据缓冲区首地址
 * @param   size —— 要发送/接收数据的字节数
 * @retval  成功返回HAL_OK
 */
//static HAL_StatusTypeDef SPI_TransmitReceive(uint8_t* send_buf, uint8_t* recv_buf, uint16_t size)
//{
//   return HAL_SPI_TransmitReceive(&hspi1, send_buf, recv_buf, size, 100);
//}



/**
 * @brief   读取Flash内部的ID
 * @param   none
 * @retval  成功返回device_id
 */
uint16_t W25QXX_ReadID(void)
{
    uint8_t recv_buf[2] = {0};    //recv_buf[0]存放Manufacture ID, recv_buf[1]存放Device ID
    uint16_t device_id = 0;
    uint8_t send_data[4] = {ManufactDeviceID_CMD,0x00,0x00,0x00};   //待发送数据，命令+地址
    
    /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);
    
    /* 发送并读取数据 */
    if (HAL_OK == SPI_Transmit(send_data, 4)) 
    {
        if (HAL_OK == SPI_Receive(recv_buf, 2)) 
        {
            device_id = (recv_buf[0] << 8) | recv_buf[1];
        }
    }
    
    /* 取消片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
    
    return device_id;
}

/**
 * @brief     读取W25QXX的状态寄存器，W25Q64一共有2个状态寄存器
 * @param     reg  —— 状态寄存器编号(1~2)
 * @retval    状态寄存器的值
 */
static uint8_t W25QXX_ReadSR(uint8_t reg)
{
    uint8_t result = 0; 
    uint8_t send_buf[4] = {0x00,0x00,0x00,0x00};
    switch(reg)
    {
        case 1:
            send_buf[0] = READ_STATU_REGISTER_1;
        case 2:
            send_buf[0] = READ_STATU_REGISTER_2;
        case 0:
        default:
            send_buf[0] = READ_STATU_REGISTER_1;
    }
    
     /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);
    
    if (HAL_OK == SPI_Transmit(send_buf, 4)) 
    {
        if (HAL_OK == SPI_Receive(&result, 1)) 
        {
            HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
            
            return result;
        }
    }
    
    /* 取消片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);

    return 0;
}


/**
 * @brief	阻塞等待Flash处于空闲状态
 * @param   none
 * @retval  none
 */
static void W25QXX_Wait_Busy(void)
{
    while((W25QXX_ReadSR(1) & 0x01) == 0x01); // 等待BUSY位清空
}

/**
 * @brief   读取SPI FLASH数据
 * @param   buffer      —— 数据存储区
 * @param   start_addr  —— 开始读取的地址(最大32bit)
 * @param   nbytes      —— 要读取的字节数(最大65535)
 * @retval  成功返回0，失败返回-1
 */
int W25QXX_Read(uint8_t* buffer, uint32_t start_addr, uint16_t nbytes)
{
    uint8_t cmd = READ_DATA_CMD;

	W25QXX_Wait_Busy();

     /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);

    SPI_Transmit(&cmd, 1);

    // 发送24位地址 (高字节在前)
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((start_addr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((start_addr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(start_addr & 0xFF);

    if (HAL_OK == SPI_Transmit(addr_bytes, 3))
    {
        if (HAL_OK == SPI_Receive(buffer, nbytes))
        {
            HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
            return 0;
        }
    }

    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
    return -1;
}

/**
 * @brief    W25QXX写使能,将S1寄存器的WEL置位
 * @param    none
 * @retval
 */
void W25QXX_Write_Enable(void)
{
    uint8_t cmd= WRITE_ENABLE_CMD;
    
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);
    
    SPI_Transmit(&cmd, 1);
    
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
    
    W25QXX_Wait_Busy();

}

/**
 * @brief    W25QXX写禁止,将WEL清零
 * @param    none
 * @retval    none
 */
void W25QXX_Write_Disable(void)
{
    uint8_t cmd = WRITE_DISABLE_CMD;

    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);
    
    SPI_Transmit(&cmd, 1);
    
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
    
    W25QXX_Wait_Busy();
}

/**
 * @brief    W25QXX擦除一个扇区
 * @param   sector_addr    —— 扇区地址 根据实际容量设置
 * @retval  none
 * @note    阻塞操作
 */
void W25QXX_Erase_Sector(uint32_t sector_addr)
{
    uint8_t cmd = SECTOR_ERASE_CMD;
    uint32_t erase_addr;

    // 计算擦除地址 (每个扇区4KB)
    erase_addr = sector_addr * 4096;

    W25QXX_Write_Enable();  //擦除操作即写入0xFF，需要开启写使能
    W25QXX_Wait_Busy();        //等待写使能完成

     /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);

    SPI_Transmit(&cmd, 1);

    // 发送24位地址 (高字节在前)
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((erase_addr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((erase_addr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(erase_addr & 0xFF);

    SPI_Transmit(addr_bytes, 3);

    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);

    W25QXX_Wait_Busy();       //等待扇区擦除完成
}


/**
 * @brief    页写入操作
 * @param    dat —— 要写入的数据缓冲区首地址
 * @param    WriteAddr —— 要写入的地址
 * @param   byte_to_write —— 要写入的字节数（0-256）
 * @retval    none
 */
void W25QXX_Page_Program(uint8_t* dat, uint32_t WriteAddr, uint16_t nbytes)
{
    uint8_t cmd = PAGE_PROGRAM_CMD;

    W25QXX_Write_Enable();

    /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);

    SPI_Transmit(&cmd, 1);

    // 发送24位地址 (高字节在前)
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((WriteAddr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((WriteAddr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(WriteAddr & 0xFF);

    SPI_Transmit(addr_bytes, 3);

    SPI_Transmit(dat, nbytes);

    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);

    W25QXX_Wait_Busy();
}

/**
 * @brief    写入两个int32_t数据到指定扇区
 * @param    data1 —— 第一个int32_t数据
 * @param    data2 —— 第二个int32_t数据
 * @param    sector_addr —— 扇区地址(0-2047)
 * @retval   成功返回0，失败返回-1
 * @note     会先擦除整个扇区，然后写入8字节数据到扇区起始位置
 */
int W25QXX_Write_Int32_Pair(int32_t data1, int32_t data2, uint32_t sector_addr)
{
    uint8_t write_buffer[8]; // 8字节缓冲区存储两个int32_t
    uint32_t write_addr;

    // 检查扇区地址范围 (W25Q64有2048个扇区)
    if(sector_addr >= 2048) {
        return -1;
    }

    // 计算写入地址 (每个扇区4KB，扇区起始地址)
    write_addr = sector_addr * 4096;

    // 将两个int32_t数据转换为字节数组 (小端序)
    write_buffer[0] = (uint8_t)(data1 & 0xFF);
    write_buffer[1] = (uint8_t)((data1 >> 8) & 0xFF);
    write_buffer[2] = (uint8_t)((data1 >> 16) & 0xFF);
    write_buffer[3] = (uint8_t)((data1 >> 24) & 0xFF);

    write_buffer[4] = (uint8_t)(data2 & 0xFF);
    write_buffer[5] = (uint8_t)((data2 >> 8) & 0xFF);
    write_buffer[6] = (uint8_t)((data2 >> 16) & 0xFF);
    write_buffer[7] = (uint8_t)((data2 >> 24) & 0xFF);

    // 先擦除扇区
    W25QXX_Erase_Sector(sector_addr);

    // 写入数据 (修正地址处理，不需要左移8位)
    uint8_t cmd = PAGE_PROGRAM_CMD;

    W25QXX_Write_Enable();

    /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);

    // 发送页编程命令
    if(HAL_OK != SPI_Transmit(&cmd, 1)) {
        HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
        return -1;
    }

    // 发送24位地址 (高字节在前)
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((write_addr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((write_addr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(write_addr & 0xFF);

    if(HAL_OK != SPI_Transmit(addr_bytes, 3)) {
        HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
        return -1;
    }

    // 发送数据
    if(HAL_OK != SPI_Transmit(write_buffer, 8)) {
        HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
        return -1;
    }

    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);

    W25QXX_Wait_Busy();

    return 0;
}

/**
 * @brief    从指定扇区读取两个int32_t数据
 * @param    data1 —— 指向第一个int32_t数据的指针
 * @param    data2 —— 指向第二个int32_t数据的指针
 * @param    sector_addr —— 扇区地址(0-2047)
 * @retval   成功返回0，失败返回-1
 * @note     从扇区起始位置读取8字节数据并转换为两个int32_t
 */
int W25QXX_Read_Int32_Pair(int32_t* data1, int32_t* data2, uint32_t sector_addr)
{
    uint8_t read_buffer[8]; // 8字节缓冲区
    uint32_t read_addr;

    // 检查参数有效性
    if(data1 == NULL || data2 == NULL || sector_addr >= 2048) {
        return -1;
    }

    // 计算读取地址 (每个扇区4KB，扇区起始地址)
    read_addr = sector_addr * 4096;

    // 读取数据 (修正地址处理，不需要左移8位)
    uint8_t cmd = READ_DATA_CMD;

    W25QXX_Wait_Busy();

    /* 使能片选 */
    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_RESET);

    // 发送读取命令
    if(HAL_OK != SPI_Transmit(&cmd, 1)) {
        HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
        return -1;
    }

    // 发送24位地址 (高字节在前)
    uint8_t addr_bytes[3];
    addr_bytes[0] = (uint8_t)((read_addr >> 16) & 0xFF);
    addr_bytes[1] = (uint8_t)((read_addr >> 8) & 0xFF);
    addr_bytes[2] = (uint8_t)(read_addr & 0xFF);

    if(HAL_OK != SPI_Transmit(addr_bytes, 3)) {
        HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
        return -1;
    }

    // 接收数据
    if(HAL_OK != SPI_Receive(read_buffer, 8)) {
        HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);
        return -1;
    }

    HAL_GPIO_WritePin(W25Q64_CHIP_SELECT_GPIO_Port, W25Q64_CHIP_SELECT_Pin, GPIO_PIN_SET);

    // 将字节数组转换为两个int32_t数据 (小端序)
    *data1 = (int32_t)(read_buffer[0] |
                      (read_buffer[1] << 8) |
                      (read_buffer[2] << 16) |
                      (read_buffer[3] << 24));

    *data2 = (int32_t)(read_buffer[4] |
                      (read_buffer[5] << 8) |
                      (read_buffer[6] << 16) |
                      (read_buffer[7] << 24));

    return 0;
}

