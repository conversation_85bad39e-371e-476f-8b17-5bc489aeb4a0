#include "app_motor.h"

void set_corner_compensation(uint8_t corner_index, float gimbal_angle, float car_angle);

/**
 * @brief 电机初始化函数
 */
void Motor_Init(void)
{
    /* 使能X轴电机 */
    Emm_V5_En_Control(&MOTOR_X_UART, MOTOR_X_ADDR, true, MOTOR_SYNC_FLAG);

    /* 使能Y轴电机 */
    Emm_V5_En_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, MOTOR_SYNC_FLAG);

    /* 停止所有电机 */
    Motor_Stop();
}

/**
 * @brief 设置XY轴电机速度
 * @param x_percent X轴速度百分比，范围-100到100
 * @param y_percent Y轴速度百分比，范围-100到100
 */
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent)
{
    uint8_t x_dir, y_dir;
    uint16_t x_speed, y_speed;

    /* 限制输入范围 */
    if (x_percent > 100)
        x_percent = 100;
    if (x_percent < -100)
        x_percent = -100;
    if (y_percent > 100)
        y_percent = 100;
    if (y_percent < -100)
        y_percent = -100;

    /* 计算X轴方向 */
    if (x_percent >= 0)
    {
        x_dir = 0; /* CW顺时针 */
    }
    else
    {
        x_dir = 1;              /* CCW逆时针 */
        x_percent = -x_percent; /* 取绝对值 */
    }

    /* 计算Y轴方向 */
    if (y_percent >= 0)
    {
        y_dir = 0; /* CW顺时针 */
    }
    else
    {
        y_dir = 1;              /* CCW逆时针 */
        y_percent = -y_percent; /* 取绝对值 */
    }

    /* 计算实际转速(百分比转换为RPM) */
    x_speed = (uint16_t)((x_percent * MOTOR_MAX_SPEED) / 100);
    y_speed = (uint16_t)((y_percent * MOTOR_MAX_SPEED) / 100);

    /* 控制X轴电机 */
    Emm_V5_Vel_Control(&MOTOR_X_UART, MOTOR_X_ADDR, x_dir, x_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);

    /* 控制Y轴电机 */
    Emm_V5_Vel_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, y_dir, y_speed, MOTOR_ACCEL, MOTOR_SYNC_FLAG);
}

/**
 * @brief 停止所有电机
 */
void Motor_Stop(void)
{
    /* 停止X轴电机 */
    Emm_V5_Stop_Now(&MOTOR_X_UART, MOTOR_X_ADDR, MOTOR_SYNC_FLAG);

    /* 停止Y轴电机 */
    Emm_V5_Stop_Now(&MOTOR_Y_UART, MOTOR_Y_ADDR, MOTOR_SYNC_FLAG);
}

void motorA_pos_control(int32_t pos,uint16_t vel)//control x - 串口3
{
    uint8_t dir = 0;
    if(pos < 0) 
    {
        pos = -pos;
        dir = 1;
    }
    // 添加调试信息
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, dir, vel, 0, pos, 1, 0);
}
void motorB_pos_control(int32_t pos,uint16_t vel)// control y - 串口2
{
    uint8_t dir = 0;
    if(pos < 0) 
    {
        pos = -pos;
        dir = 1;
    }
    // 添加调试信息
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, dir, vel, 0, pos, 1, 0);
}
void stepmotor_remember() 
{
	Emm_V5_Origin_Set_O(&MOTOR_Y_UART,1,1);
    Emm_V5_Origin_Set_O(&MOTOR_X_UART,MOTOR_X_ADDR,1);
}
void stepmotor_backZero()
{
  Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART,MOTOR_Y_ADDR, 0, 0);
  Emm_V5_Origin_Trigger_Return(&MOTOR_X_UART,MOTOR_X_ADDR, 0, 0);
}

void motorA_xiangdui(int32_t pos)
{
    uint8_t dir = 0;
    if(pos < 0) 
    {
        pos = -pos;
        dir = 1;
    }
    // 添加调试信息
    Emm_V5_Pos_Control(&MOTOR_X_UART, MOTOR_X_ADDR, dir, 250, 0, pos, 0, 0);  // 提高速度到250 RPM
}
void motorB_xiangdui(int32_t pos)
{
    uint8_t dir = 0;
    if(pos < 0) 
    {
        pos = -pos;
        dir = 1;
    }
    // 添加调试信息
    Emm_V5_Pos_Control(&MOTOR_Y_UART, MOTOR_Y_ADDR, dir, 5, 0, pos, 0, 0);
}

void Auto_find(void)
{
	Motor_Set_Speed(task3_speed,0);
	if(camera_x_error && camera_y_error) 
	{
		Motor_Set_Speed(0,0);
		State_Machine.STATE_TASK3 = task3_state2;
	}
}
void Auto_find_task4(void)
{
	Motor_Set_Speed(task3_speed,0);
	if(camera_x_error && camera_y_error) 
	{
		Motor_Set_Speed(0,0);
		State_Machine.STATE_TASK4 = task4_state2;
	}
}
// IMU角度控制函数
void imu_angle_control(float current_yaw, float last_yaw) 
{
    float delta_yaw = current_yaw - last_yaw;
    
//    // 处理角度跳变 (359° -> 1°)
//    if (delta_yaw > 180.0f) delta_yaw -= 360.0f;
//    if (delta_yaw < -180.0f) delta_yaw += 360.0f;
//    
    // 小于阈值不控制，避免抖动
   // if (fabs(delta_yaw) < 0.1f) return;
    
    // 转换为脉冲数 (精确映射：51200脉冲=360°)
    int32_t x_pulse = angle_to_pulse(delta_yaw);
    my_printf(&huart1,"%d\r\n",x_pulse);
    if (abs(x_pulse) > 0) 
	{
        motorA_xiangdui(-x_pulse);  // X轴云台同步转动
    }
}
// 四拐点云台补偿角度数组 (云台需要转动的角度)
float gimbal_compensation_angles[4] = {
    114.1f,  // 第1个拐点：车转90°，云台转114.1°
    70.0f,    // 第2个拐点：请填入您的数据
    78.9f,    // 第3个拐点：请填入您的数据  
    86.5f     // 第4个拐点：请填入您的数据
};

// 四拐点对应的车辆转动角度
float car_rotation_angles[4] = {
    90.0f,   // 第1个拐点：车转90°
    90.0f,   // 第2个拐点：车转90°
    90.0f,   // 第3个拐点：车转90°
    90.0f    // 第4个拐点：车转90°
};

// 当前拐点索引
static uint8_t current_corner = 0;

/**
 * @brief 四拐点IMU角度控制
 * @param current_yaw 当前IMU角度
 * @param last_yaw 上次IMU角度
 * @param in_dead_zone 是否在死区内
 */
void imu_four_corner_control(float current_yaw, float last_yaw, bool in_dead_zone)
{
    static bool last_in_dead_zone = true;
    static bool first_run = true;  // 添加首次运行标志
    // 首次运行时不切换拐点
    if(first_run)
    {
        first_run = false;
       // my_printf(&huart1, "conner:%d，angle:%.1f°\r\n", current_corner + 1, gimbal_compensation_angles[current_corner]);
    }
    // 检测是否刚离开死区（非首次运行）
    else if(last_in_dead_zone && !in_dead_zone)
    {
        // 刚离开死区，切换到下一个拐点
        current_corner = (current_corner + 1) % 4;
		my_printf(&huart1, "a\r\n");
        //my_printf(&huart1, "conner:%d，angle:%.1f°\r\n", current_corner + 1, gimbal_compensation_angles[current_corner]);
    }
    
    if(!in_dead_zone)  // 在非死区时使用IMU控制
    {
        float delta_yaw = current_yaw - last_yaw;
        
        // 处理角度跳变
        if (delta_yaw > 180.0f) delta_yaw -= 360.0f;
        if (delta_yaw < -180.0f) delta_yaw += 360.0f;
        
        // 小于阈值不控制
        if (fabs(delta_yaw) < 0.1f) return;
        
        // 计算当前拐点的补偿比例
        float compensation_ratio = gimbal_compensation_angles[current_corner] / car_rotation_angles[current_corner];
        
        // 计算云台需要转动的角度
        float gimbal_delta = delta_yaw * compensation_ratio;
        
        // 转换为脉冲数 (精确映射：51200脉冲=360°)
        int32_t x_pulse = angle_to_pulse(gimbal_delta);
        
        if (abs(x_pulse) > 0) {
            motorA_xiangdui(-x_pulse);
            my_printf(&huart1, "A%d: B%.2f° C%.2f° D%ld\r\n", current_corner + 1, delta_yaw, gimbal_delta, x_pulse);
        }
    }
    
    // 更新死区状态
    last_in_dead_zone = in_dead_zone;
}

/**
 * @brief 设置四拐点补偿角度
 * @param corner_index 拐点索引 (0-3)
 * @param gimbal_angle 云台补偿角度
 * @param car_angle 车辆转动角度
 */
void set_corner_compensation(uint8_t corner_index, float gimbal_angle, float car_angle)
{
    if(corner_index < 4)
    {
        gimbal_compensation_angles[corner_index] = gimbal_angle;
        car_rotation_angles[corner_index] = car_angle;
    }
}

/**
 * @brief 获取当前拐点信息
 */
void get_current_corner_info(void)
{
//    my_printf(&huart1, "NOW: %d\r\n", current_corner + 1);
//    my_printf(&huart1, "ANGLE: %.1f°\r\n", gimbal_compensation_angles[current_corner]);
//    my_printf(&huart1, "RATIO: %.3f\r\n", gimbal_compensation_angles[current_corner] / car_rotation_angles[current_corner]);
}

