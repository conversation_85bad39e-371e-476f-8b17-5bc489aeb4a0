/**
 * @file app_motor.h
 * @brief 基于EmmV5的电机控制函数
 * @copyright 白鹭电子工作室
 */

#ifndef __APP_MOTOR_H_
#define __APP_MOTOR_H_

#include "mydefine.h"

/* 电机控制函数定义 */
#define MOTOR_X_ADDR        0x01          // X轴电机地址
#define MOTOR_Y_ADDR        0x01          // Y轴电机地址
#define MOTOR_X_UART        huart3        // X轴电机串口
#define MOTOR_Y_UART        huart2        // Y轴电机串口
#define MOTOR_MAX_SPEED     200           // 最大转速(RPM)
#define MOTOR_ACCEL         0            // 加速度(0表示直接启动)
#define MOTOR_SYNC_FLAG     false         // 同步标志
#define MOTOR_MAX_ANGLE     50            // 最大角度限制(±50度)

/* 精确的角度-脉冲映射关系 */
#define MOTOR_PULSES_PER_REVOLUTION  51200        // 电机一圈脉冲数
#define MOTOR_DEGREES_PER_REVOLUTION 360.0f       // 电机一圈角度
#define PULSE_TO_ANGLE_RATIO        (MOTOR_DEGREES_PER_REVOLUTION / MOTOR_PULSES_PER_REVOLUTION)  // 0.00703125°/脉冲

/* 角度转换函数 */
static inline int32_t angle_to_pulse(float angle) {
    return (int32_t)(angle / PULSE_TO_ANGLE_RATIO + 0.5f);
}

static inline float pulse_to_angle(int32_t pulse) {
    return pulse * PULSE_TO_ANGLE_RATIO;
}

/* 函数声明 */
void Motor_Init(void);                    // 电机初始化
void Motor_Set_Speed(int8_t x_percent, int8_t y_percent);  // 设置XY轴速度(百分比)
void Motor_Stop(void);                    // 停止所有电机
void motorA_pos_control(int32_t pos,uint16_t vel);//dir 0-1 pos 0 - 3200 //control x
void motorB_pos_control(int32_t pos,uint16_t vel);//dir 0-1 pos 0 - 3200 // control y
void stepmotor_remember(void);
void stepmotor_backZero(void);
void motorA_xiangdui(int32_t pos);//相对位置模式
void motorB_xiangdui(int32_t pos);//相对位置模式
void Auto_find(void);
void Auto_find_task4(void);
void imu_angle_control(float current_yaw, float last_yaw);
void set_corner_compensation(uint8_t corner_index, float gimbal_angle, float car_angle);
void imu_four_corner_control(float current_yaw, float last_yaw, bool in_dead_zone);
#endif /* __APP_MOTOR_H_ */

